<template>
  <div class="acts" style="background-color: #ede9e7; min-height: 100vh">
    <div class="pageTop">{{ $t('mode.zanzhu.title') }}</div>
    <div style="height: 30px"></div>
    <div class="lis" @click="$parent.goNav('/zhanzhuye?type=1')">
      <div class="lfs">
        <img src="/static/image/ddf471901f2b4fff9ee57015a1698227.png" />
        <div class="you">{{ $t('mode.zanzhu.label1') }}</div>
        <div class="te">{{ $t('mode.zanzhu.label2') }}</div>
      </div>
      <div class="rig">
        <img src="/static/image/93b000fa1d3246ce9b90a62c018714af.png" alt="" />
      </div>
    </div>
     <div class="lis" @click="$parent.goNav('/zhanzhuye?type=2')">
      <div class="lfs">
        <img src="/static/image/ddf471901f2b4fff9ee57015a1698227.png" />
        <div class="you">{{ $t('mode.zanzhu.label3') }}</div>
        <div class="te">{{ $t('mode.zanzhu.label4') }}</div>
      </div>
      <div class="rig">
        <img src="/static/image/bd72c14c428d41ce8105a0d82a1bb696.png" alt="" />
      </div>
    </div>
      <van-divider dashed :style="{ color: '#ccc', borderColor: '#ccc', padding: '20px 100px' }">{{ $t('mode.zanzhu.label5') }}~</van-divider>

  </div>
</template>
<script>
export default {
  name: 'zhanzhu',
  data() {
    return {};
  },
  created() {
    let that = this;
  },
  methods: {},
  mounted() {
    let that = this;
  },
  updated() {
    let that = this;
  },
  beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
.lis {
  display: flex;
  width: calc(100% - 40px);
  margin: 0 auto;
  margin-top: 20px;
  box-sizing: border-box;

  border-radius: 20px;
  overflow: hidden;
  background-color: #f8f9ff;
  -webkit-box-shadow: 0 0.04rem 0.2rem rgb(199 212 255 / 42%);
  box-shadow: 0 0.04rem 0.2rem rgb(199 212 255 / 42%);
  border: 0.02rem solid #fff;
  .lfs {
    height: 100%;
    width: 45%;
    text-align: center;
    color: #98a8c5;
    img {
      width: 30px;
      display: block;
      margin: 0 auto;
      margin-top: 20px;
    }
    .you {
      font-weight: 700;
      font-size: 18px;
      margin-top: 10px;
    }
    .te {
      font-size: 12px;
      margin-top: 5px;
    }
  }
  .rig {
    width: 55%;
    font-size: 0;
    img {
      width: 100%;
    }
  }
}
</style>
