<template>
  <main>
    <div class="pageTop">彩種大廳</div>
    <div class="nav">
      <div class="block" :class="{ active: navActive === 0 }" @click="changeNav(0)">
        <div class="label label2">全部彩種</div>
      </div>
      <div class="block" :class="{ active: navActive === 1 }" @click="changeNav(1)">
        <div class="label">賽車PK拾</div>
      </div>
      <div class="block" :class="{ active: navActive === 2 }" @click="changeNav(2)">
        <div class="label">快三</div>
      </div>
    </div>

    <div class="lotteryList">
      <van-swipe class="my-swipe" :loop="false" :show-indicators="false" ref="swipe" @change="e => {this.navActive = e}">
        <van-swipe-item>
          <div class="card">
            <div class="block" v-for="(item, index) in totalList" :key="index" @click="goPage(item)">
              <img :src="item.logo" alt="">
              <div class="label">{{ item.title }}</div>
            </div>
          </div>
        </van-swipe-item>
        <van-swipe-item>
          <div class="card">
            <div class="block" v-for="(item, index) in pk10List" :key="index" @click="goPage(item)">
              <img :src="item.logo" alt="">
              <div class="label">{{ item.title }}</div>
            </div>
          </div>
        </van-swipe-item>
        <van-swipe-item>
          <div class="card">
            <div class="block" v-for="(item, index) in k3List" :key="index" @click="goPage(item)">
              <img :src="item.logo" alt="">
              <div class="label">{{ item.title }}</div>
            </div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
  </main>
</template>
<script>
export default {
  name: 'caipiao',
  data() {
    return {
      navActive: 0,
      totalList: [],
      pk10List: [],
      k3List: [],
    };
  },
  created() {
    this.$parent.showLoading();
    this.getCaipiaoList()
    const query = this.$route.query;
    if(query.type === 'k3') {
      this.navActive = 2
    }else if(query.type === 'pk10') {
      this.navActive = 1
    }
    this.$nextTick(() => {
      this.$refs.swipe.swipeTo(this.navActive)
    })
  },
  methods: {
    async getCaipiaoList() {
      const res = await this.$apiFun.getCaipiaoListApi()
      const k3List = []
      const pk10List = []
      for(let i in res.data) {
        if(res.data[i].typeid === 'k3') {
          k3List.push(res.data[i])
        }else {
          pk10List.push(res.data[i])
        }
      }
      this.totalList = res.data
      this.pk10List = pk10List
      this.k3List = k3List

      this.$parent.hideLoading();
    },

    changeNav(index) {
      this.navActive = index
      this.$refs.swipe.swipeTo(index)
      console.log(this.$refs.swipe)
    },

    goPage(e) {
      console.log(e)
      this.$parent.goNav(`/${e.typeid}?name=${e.name}`)
    }
  },
  mounted() {
  },
};
</script>
<style lang="scss" scoped>
.pageTop {
  background: #fff;
}
.nav {
  height: 55px;
  display: flex;
  margin: 25px 15px 0;
  .block {
    justify-content: center;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-right: 20px;
    .label {
      color: #606060;
      width: 100%;
      text-align: center;
      margin-top: 15px;
      font-size: .35rem;
    }
  }
  .active {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 4px;
      border-radius: 4px;
      background: #CF866B;
      bottom: 2px;
    }
    .label {
      color: #3f4042;
      font-weight: 500;
    }
  }
}
.lotteryList {
  padding-top: 10px;
  background: #F1F1F1;
  height: calc(var(--vh) * 100 - 80px);
  overflow-y: auto;
  box-sizing: border-box;
}
.card {
  display: flex;
  flex-wrap: wrap;
  margin: 0 15px;
  gap: 15px;
  .block {
    width: calc((100%  - 30px) / 3);
    border-radius: 10px;
    text-align: center;
    box-sizing: border-box;
    position: relative;
    img {
      width: 100%;
      display: block;
      margin: 0 auto;
      object-fit: contain;
    }
    .label {
      font-size: 13px;
      position: absolute;
      text-align: left;
      color: #4E6693;
      font-weight: 500;
      padding: 8px;
      left: 0;
      top: 0;
    }
  }
}
</style>
