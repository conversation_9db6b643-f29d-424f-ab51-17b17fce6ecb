import axios from 'axios'
import router from './../router'
// 创建axios实例
const service = axios.create({
  timeout: 20000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    console.log(config)
    // 可以在这里添加请求头等信息
    const token = localStorage.getItem('token')
    // config.headers.Authorization = token ? `Bearer ${token}` : undefined
    config.headers.token = token
    return config
  },
  (error) => {
    // 请求错误处理
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 对响应数据做处理，例如只返回data部分
    const res = response.data
    // 根据业务判断是否需要进行错误处理
    if (res.code === 401) {
      // 清除token 以及用户信息
      localStorage.removeItem('token')
      setTimeout(() => {
        router.replace('/login');
      }, 500)
    }
    return res
  },
  (error) => {
    console.log(error)
    const res = error.response.data
    // token 过期处理
    if (res.code === 401) {
      // 清除token 以及用户信息
      localStorage.removeItem('token')
      setTimeout(() => {
        router.replace('/login');
      }, 1500)
      return Promise.reject(error)
    }
    // 响应错误处理
    console.log('err' + error) // for debug
    return Promise.reject(error)
  }
)

export default service
